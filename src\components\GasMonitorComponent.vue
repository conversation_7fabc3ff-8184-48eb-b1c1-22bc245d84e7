<template>
  <div class="gas-monitor-container">
    <!-- 头部区域 -->
    <div class="header-section">
      <div class="left-section">
        <div class="realtime-label">实时</div>
        <div class="date-picker-wrapper">
          <el-date-picker v-model="selectedDate" type="date" placeholder="选择日期" format="YYYY.MM.DD"
            value-format="YYYY-MM-DD" @change="handleDateChange" class="custom-date-picker" />
        </div>
      </div>
    </div>

    <!-- 数据展示区 -->
    <div class="data-display-section">
      <!-- CO仪表盘 -->
      <div class="gauge-item">
        <div ref="coGaugeRef" class="gauge-container"></div>
        <div class="gauge-label">CO</div>
      </div>

      <!-- CO₂仪表盘 -->
      <div class="gauge-item">
        <div ref="co2GaugeRef" class="gauge-container"></div>
        <div class="gauge-label">CO₂</div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue'
import * as echarts from 'echarts'
import { ElDatePicker } from 'element-plus'

// Props
const props = defineProps({
  // CO数值
  coValue: {
    type: Number,
    default: 12
  },
  // CO₂数值
  co2Value: {
    type: Number,
    default: 36
  },
  // 初始日期
  initialDate: {
    type: String,
    default: '2025-07-01'
  }
})

// Emits
const emit = defineEmits(['dateChange', 'dataUpdate'])

// 响应式数据
const selectedDate = ref(props.initialDate)
const coGaugeRef = ref(null)
const co2GaugeRef = ref(null)
const coGaugeInstance = ref(null)
const co2GaugeInstance = ref(null)

// 处理日期变化
const handleDateChange = (date) => {
  emit('dateChange', date)
}

// 创建仪表盘配置 - 基于dashboard.html的样式
const createGaugeOption = (value, label, maxValue = 100) => {
  const percentage = value / maxValue
  return {
    backgroundColor: '#fff',

    series: [{
      type: 'gauge',
      radius: '60%',
      startAngle: '215',
      endAngle: '-35',
      splitNumber: 50,
      detail: {
        offsetCenter: [0, -20],
        formatter: ' '
      },
      pointer: {
        show: false
      },
      axisLine: {
        show: true,
        lineStyle: {
          color: [
            [0, '#7691FA'],
            [percentage, '#7691FA'],
            [1, '#e9e9e9']
          ],
          width: 45
        }
      },
      axisTick: {
        show: false
      },
      splitLine: {
        show: true,
        length: 45,
        lineStyle: {
          color: '#fff',
          width: 6
        }
      },
      axisLabel: {
        show: false
      }
    },
    {
      type: 'gauge',
      radius: '48%',
      startAngle: '212',
      endAngle: '-32',
      splitNumber: 45,
      pointer: {
        show: false
      },
      detail: {
        offsetCenter: [0, -5],
        formatter: `{a|${label}}\n{b|${value}}\n`,
        rich: {
          a: {
            color: '#404346',
            lineHeight: 35,
            fontSize: 22,
            fontWeight: 550
          },
          b: {
            color: '#7691FA',
            fontSize: 32,
            fontWeight: 550,
            padding: [10, 0, 10, 0]
          }
        }
      },
      axisLine: {
        show: false,
        lineStyle: {
          color: [
            [0, '#e9e9e9'],
            [1, '#e9e9e9']
          ],
          width: 8
        }
      },
      axisTick: {
        show: false
      },
      splitLine: {
        show: true,
        length: 8,
        lineStyle: {
          color: '#fff',
          width: 5
        }
      },
      axisLabel: {
        show: false
      }
    }]
  }
}

// 初始化仪表盘
const initGauges = () => {
  if (coGaugeRef.value) {
    coGaugeInstance.value = echarts.init(coGaugeRef.value)
    const coOption = createGaugeOption(props.coValue, 'CO', 100)
    coGaugeInstance.value.setOption(coOption)
  }

  if (co2GaugeRef.value) {
    co2GaugeInstance.value = echarts.init(co2GaugeRef.value)
    const co2Option = createGaugeOption(props.co2Value, 'CO₂', 100)
    co2GaugeInstance.value.setOption(co2Option)
  }
}

// 更新仪表盘数据
const updateGauges = () => {
  if (coGaugeInstance.value) {
    const coOption = createGaugeOption(props.coValue, 'CO', 100)
    coGaugeInstance.value.setOption(coOption)
  }

  if (co2GaugeInstance.value) {
    const co2Option = createGaugeOption(props.co2Value, 'CO₂', 100)
    co2GaugeInstance.value.setOption(co2Option)
  }
}

// 监听窗口大小变化
const handleResize = () => {
  if (coGaugeInstance.value) {
    coGaugeInstance.value.resize()
  }
  if (co2GaugeInstance.value) {
    co2GaugeInstance.value.resize()
  }
}

// 监听props变化
watch(() => [props.coValue, props.co2Value], () => {
  updateGauges()
}, { deep: true })

watch(() => props.initialDate, (newDate) => {
  selectedDate.value = newDate
})

// 生命周期
onMounted(() => {
  nextTick(() => {
    initGauges()
    window.addEventListener('resize', handleResize)
  })
})

onUnmounted(() => {
  if (coGaugeInstance.value) {
    coGaugeInstance.value.dispose()
  }
  if (co2GaugeInstance.value) {
    co2GaugeInstance.value.dispose()
  }
  window.removeEventListener('resize', handleResize)
})
</script>

<style scoped lang="scss">
@use '@/assets/base.scss' as *;

.gas-monitor-container {
  width: 100%;
  background: #FFFFFF;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

// 头部区域
.header-section {
  margin-bottom: 10px;
  padding-bottom: 15px;
}

.left-section {
  display: flex;
  align-items: center;
  gap: 20px;
}

.realtime-label {
  color: #333333;
  font-size: 18px;
  font-weight: 600;
}

.date-picker-wrapper {
  display: flex;
  align-items: center;
}

.custom-date-picker {
  width: 150px;

  :deep(.el-input__wrapper) {
    background: #ffffff;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    box-shadow: none;

    &:hover {
      border-color: #4A90E2;
    }

    &.is-focus {
      border-color: #4A90E2;
    }
  }

  :deep(.el-input__inner) {
    color: #333333;
    font-size: 14px;

    &::placeholder {
      color: #a8abb2;
    }
  }
}

// 数据展示区
.data-display-section {
  display: flex;
  gap: 40px;
  justify-content: center;
  align-items: center;
}

.gauge-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15px;
}

.gauge-container {
  width: 180px;
  height: 180px;
  background: #ffffff;
  border-radius: 8px;
  border: 1px solid rgba(74, 144, 226, 0.2);
}

.gauge-label {
  color: #333333;
  font-size: 18px;
  font-weight: 600;
  text-align: center;
}

// 响应式设计
@media (max-width: 768px) {
  .data-display-section {
    flex-direction: column;
    gap: 30px;
  }

  .gauge-container {
    width: 150px;
    height: 150px;
  }

  .gauge-label {
    font-size: 16px;
  }

  .header-section {
    .left-section {
      flex-direction: column;
      gap: 10px;
      align-items: flex-start;
    }
  }
}
</style>
