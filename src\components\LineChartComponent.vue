<template>
  <div class="line-chart-container">
    <!-- 时间筛选栏 -->
    <div class="time-filter-section">
      <div class="filter-buttons">
        <button v-for="option in timeOptions.filter(opt => opt.value !== 'custom')" :key="option.value"
          :class="['filter-btn', { active: selectedTimeRange === option.value }]"
          @click="selectTimeRange(option.value)">
          {{ option.label }}
        </button>
        <div class="date-range-section">
          <el-date-picker v-model="customDateRange" type="daterange" range-separator="至" start-placeholder="开始日期"
            end-placeholder="结束日期" format="YYYY-MM-DD" value-format="YYYY-MM-DD" @change="updateCustomRange"
            class="custom-date-picker" />
        </div>
      </div>

    </div>

    <!-- 指标数据栏 -->
    <div class="indicators-section">
      <div v-for="indicator in filteredIndicators" :key="indicator.name" class="indicator-item" >
        <div class="indicator-name">{{ indicator.name }}</div>
        <div class="indicator-value"><span>{{ indicator.value }}</span>{{ indicator.unit }}</div>
      </div>
    </div>

    <!-- ECharts 折线图 -->
    <div class="chart-section">
      <div ref="chartRef" class="chart-container" :style="{ height: chartHeight }"></div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, watch, nextTick } from 'vue'
import * as echarts from 'echarts'
import { ElDatePicker } from 'element-plus'

// Props
const props = defineProps({
  // 指标数据
  indicatorData: {
    type: Object,
    default: () => ({})
  },
  // 图表数据
  chartData: {
    type: Object,
    default: () => ({
      times: ['18:00', '18:01', '18:02', '18:03', '18:04', '18:05'],
      values: [25.6, 26.1, 25.8, 26.3, 25.9, 26.0]
    })
  },
  // 图表高度
  height: {
    type: [String, Number],
    default: 250
  }
})

// Emits
const emit = defineEmits(['timeRangeChange', 'dataUpdate'])

// 响应式数据
const chartRef = ref(null)
const chartInstance = ref(null)
const selectedTimeRange = ref('realtime')
const customDateRange = ref([])  // 改为数组格式，用于Element Plus的daterange

// 时间选项
const timeOptions = [
  { label: '实时', value: 'realtime' },
  { label: '近一天', value: 'day' },
  { label: '近一周', value: 'week' },
  { label: '近一月', value: 'month' },
  { label: '自定义', value: 'custom' }
]

// 指标数据
const indicators = ref([
  { name: '温度', value: props.indicatorData.temperature, unit: '℃' },
  { name: '噪音', value: props.indicatorData.noise, unit: 'dB' },
  { name: '氯化氢', value: props.indicatorData.hcl, unit: 'dB' },
  { name: '湿度', value: props.indicatorData.humidity, unit: 'dB' },
  { name: 'TVOC', value: props.indicatorData.tvoc, unit: 'dB' },
  { name: '放射', value: props.indicatorData.radiation, unit: 'uSv/s' }
])

// 过滤有值的指标数据
const filteredIndicators = computed(() => {
  return indicators.value.filter(indicator =>
    indicator.value !== undefined &&
    indicator.value !== null &&
    indicator.value !== ''
  )
})

// 计算图表高度
const chartHeight = computed(() => {
  if (typeof props.height === 'number') {
    return `${props.height}px`
  }
  return props.height
})

// 选择时间范围
const selectTimeRange = (value) => {
  selectedTimeRange.value = value
  if (value !== 'custom') {
    emit('timeRangeChange', { type: value })
    updateChartData()
  }
}

// 更新自定义时间范围
const updateCustomRange = (dateRange) => {
  if (dateRange && dateRange.length === 2) {
    emit('timeRangeChange', {
      type: 'custom',
      startDate: dateRange[0],
      endDate: dateRange[1]
    })
    updateChartData()
  }
}

// 初始化图表
const initChart = () => {
  if (!chartRef.value) return

  chartInstance.value = echarts.init(chartRef.value)

  // 确保数据格式正确
  const times = props.chartData?.times || []
  const values = (props.chartData?.values || []).map(v => Number(v))

  const option = {
    backgroundColor: 'transparent',
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '10%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: times,
      axisLine: {
        lineStyle: {
          color: '#4A90E2'
        }
      },
      axisLabel: {
        color: '#666666',
        fontSize: 12
      }
    },
    yAxis: {
      type: 'value',
      axisLine: {
        lineStyle: {
          color: '#4A90E2'
        }
      },
      axisLabel: {
        color: '#666666',
        fontSize: 12
      },
      splitLine: {
        lineStyle: {
          color: 'rgba(74, 144, 226, 0.3)'
        }
      }
    },
    series: [{
      data: values,
      type: 'line',
      smooth: true,
      lineStyle: {
        color: '#4A90E2',
        width: 2
      },
      itemStyle: {
        color: '#4A90E2'
      },
      areaStyle: {
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [{
            offset: 0, color: 'rgba(74, 144, 226, 0.3)'
          }, {
            offset: 1, color: 'rgba(74, 144, 226, 0.05)'
          }]
        }
      }
    }],
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(0, 0, 0, 0.8)',
      borderColor: '#4A90E2',
      textStyle: {
        color: '#333333'
      },
      formatter: function (params) {
        if (params && params.length > 0) {
          const param = params[0]
          return `时间: ${param.name}<br/>数值: ${param.value}`
        }
        return ''
      }
    }
  }

  chartInstance.value.setOption(option)
}

// 更新图表数据
const updateChartData = () => {
  if (!chartInstance.value) return

  // 这里可以根据时间范围生成不同的模拟数据
  let newData = generateMockData(selectedTimeRange.value)

  // 确保数据格式正确
  const times = newData.times || []
  const values = (newData.values || []).map(v => Number(v))

  chartInstance.value.setOption({
    xAxis: {
      data: times
    },
    series: [{
      data: values,
      type: 'line'
    }]
  })
}

// 生成模拟数据
const generateMockData = (timeRange) => {
  const now = new Date()
  let times = []
  let values = []

  switch (timeRange) {
    case 'realtime':
      // 实时数据 - 最近6个时间点
      for (let i = 5; i >= 0; i--) {
        const time = new Date(now.getTime() - i * 60000) // 每分钟一个点
        times.push(time.toTimeString().slice(0, 5))
        values.push(Number((Math.random() * 5 + 23).toFixed(1)))
      }
      break
    case 'day':
      // 近一天数据 - 每小时一个点
      for (let i = 23; i >= 0; i--) {
        const time = new Date(now.getTime() - i * 3600000)
        times.push(time.toTimeString().slice(0, 5))
        values.push(Number((Math.random() * 10 + 20).toFixed(1)))
      }
      break
    case 'week':
      // 近一周数据 - 每天一个点
      for (let i = 6; i >= 0; i--) {
        const time = new Date(now.getTime() - i * 86400000)
        times.push(`${time.getMonth() + 1}/${time.getDate()}`)
        values.push(Number((Math.random() * 15 + 15).toFixed(1)))
      }
      break
    case 'month':
      // 近一月数据 - 每5天一个点
      for (let i = 30; i >= 0; i -= 5) {
        const time = new Date(now.getTime() - i * 86400000)
        times.push(`${time.getMonth() + 1}/${time.getDate()}`)
        values.push(Number((Math.random() * 20 + 10).toFixed(1)))
      }
      break
    default:
      times = props.chartData.times || []
      values = props.chartData.values || []
  }

  return { times, values }
}

// 监听窗口大小变化
const handleResize = () => {
  if (chartInstance.value) {
    chartInstance.value.resize()
  }
}

// 监听props变化
watch(() => props.indicatorData, (newData) => {
  indicators.value = [
    { name: '温度', value: newData.temperature, unit: '℃' },
    { name: '噪音', value: newData.noise, unit: 'dB' },
    { name: '氯化氢', value: newData.hcl, unit: 'dB' },
    { name: '湿度', value: newData.humidity, unit: 'dB' },
    { name: 'TVOC', value: newData.tvoc, unit: 'dB' },
    { name: '放射', value: newData.radiation, unit: 'μSv/h' }
  ]
}, { deep: true })

watch(() => props.chartData, (newData) => {
  if (newData && chartInstance.value) {
    updateChartData()
  }
}, { deep: true })

// 生命周期
onMounted(() => {
  nextTick(() => {
    // 确保DOM元素已经渲染
    if (chartRef.value) {
      initChart()
      window.addEventListener('resize', handleResize)
    }
  })
})

onUnmounted(() => {
  if (chartInstance.value) {
    chartInstance.value.dispose()
  }
  window.removeEventListener('resize', handleResize)
})
</script>

<style scoped lang="scss">
@use '@/assets/base.scss' as *;

.line-chart-container {
  width: 100%;
  background: #FFFFFF;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

// 时间筛选栏
.time-filter-section {
  margin-bottom: 20px;
}

.filter-buttons {
  display: flex;
  gap: 12px;
  margin-bottom: 10px;
  align-items: center;
}

.filter-btn {
  padding: 8px 16px;
  background: none;
  border: none;
  border-radius: 4px;
  color: #333333;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 18px;

  &:hover {
    background: rgba(74, 144, 226, 0.2);
    border-color: #1890FF;
  }

  &.active {
    // background: #4A90E2;
    // border-color: #4A90E2;
    color: #1890FF;
    font-weight: bold;
  }
}

.date-range-section {
  display: flex;
  align-items: center;
  gap: 10px;
}

.custom-date-picker {
  width: 280px;

  :deep(.el-input__wrapper) {
    background: #ffffff;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    box-shadow: none;

    &:hover {
      border-color: #4A90E2;
    }

    &.is-focus {
      border-color: #4A90E2;
    }
  }

  :deep(.el-range-input) {
    color: #333333;
    font-size: 18px;

    &::placeholder {
      color: #a8abb2;
      font-size: 18px;
    }
  }

  :deep(.el-range-separator) {
    color: #333333;
  }

  :deep(.el-range-input) {
    color: #333333;
    background: transparent;

    &::placeholder {
      color: #a8abb2;
    }
  }
}

// 指标数据栏
.indicators-section {
  gap: 20px;
  margin-bottom: 10px;
  display: flex;
  flex-direction: row;
}

.indicator-item {
  // flex: 1;
  min-width: 100px;
  max-width: 220px;
  background: #E7F3FF;
  border: 0px solid #040404;
  border-radius: 6px;
  padding: 5px 15px;
  text-align: center;
  display: flex;
  justify-content: center;
  align-items: center;
}

.indicator-name {
  color: #333333;
  font-size: 16px;
  font-weight: 500;
  margin-right: 6px;
}

.indicator-value {
  font-weight: bold;
  font-family: MicrosoftYaHei, MicrosoftYaHei;
  font-size: 24px;
  color: #349EFE;

  span {
    font-size: 32px;
  }
}

// 图表区域
.chart-section {
  width: 100%;
}

.chart-container {
  width: 100%;
  // height由内联样式控制，不在这里设置固定值
}

// 响应式设计
@media (max-width: 1200px) {
  .indicators-section {
    gap: 15px;
  }

  .indicator-item {
    min-width: 100px;
  }

  .chart-container {
    height: 250px;
  }
}

@media (max-width: 768px) {
  .filter-buttons {
    flex-wrap: wrap;
    gap: 8px;
  }

  .filter-btn {
    padding: 6px 12px;
    font-size: 12px;
  }

  .indicators-section {
    gap: 10px;
  }

  .indicator-item {
    min-width: 80px;
    padding: 8px;
  }

  .indicator-name {
    font-size: 12px;
  }

  .indicator-value {
    font-size: 16px;
  }

  .chart-container {
    height: 200px;
  }
}
</style>
